"use client";

import { useEffect, useState } from "react";

import type { ENV_TYPE } from "@/app/app.constants";
import type { GlobalVmConfig } from "@/app/utils/vm-config";
import { getCurrentVmConfig, updateVmConfig } from "@/app/utils/vm-config";

import { AppButton } from "./app-button";

interface VmButtonsProps {
  fuzzerType: ENV_TYPE;
  index?: number;
  localVmConfig?: GlobalVmConfig;
  onLocalVmConfigChange?: (index: number, config: GlobalVmConfig) => void;
  className?: string;
  mode?: "global" | "local"; // New prop to determine behavior
}

export function VmButtons({
  fuzzerType,
  index = 0,
  localVmConfig,
  onLocalVmConfigChange,
  className = "",
  mode = "local",
}: VmButtonsProps) {
  const [globalVmConfig, setGlobalVmConfig] = useState<GlobalVmConfig>({
    prank: false,
    roll: false,
    time: false,
  });
  const [isInitialized, setIsInitialized] = useState(false);

  // Load global configuration on mount and when fuzzer type changes
  useEffect(() => {
    const currentConfig = getCurrentVmConfig(fuzzerType);
    setGlobalVmConfig({
      prank: currentConfig.prank,
      roll: currentConfig.roll,
      time: currentConfig.time,
    });
    setIsInitialized(true);
  }, [fuzzerType]);

  // Listen for external config changes
  useEffect(() => {
    const handleConfigChange = (event: CustomEvent) => {
      const newConfig = event.detail as GlobalVmConfig;
      setGlobalVmConfig(newConfig);
    };

    const handleStorageChange = () => {
      const currentConfig = getCurrentVmConfig(fuzzerType);
      setGlobalVmConfig({
        prank: currentConfig.prank,
        roll: currentConfig.roll,
        time: currentConfig.time,
      });
    };

    window.addEventListener(
      "vm-config-changed",
      handleConfigChange as EventListener
    );
    window.addEventListener("storage", handleStorageChange);

    return () => {
      window.removeEventListener(
        "vm-config-changed",
        handleConfigChange as EventListener
      );
      window.removeEventListener("storage", handleStorageChange);
    };
  }, [fuzzerType]);

  const effectiveVmConfig = localVmConfig || globalVmConfig;

  const handleToggle = (property: keyof GlobalVmConfig) => {
    if (mode === "global") {
      // Get the current configuration from storage to ensure we have the latest state
      const currentConfig = getCurrentVmConfig(fuzzerType);
      const newGlobalConfig = updateVmConfig(currentConfig, {
        [property]: !currentConfig[property],
      });
      setGlobalVmConfig({
        prank: newGlobalConfig.prank,
        roll: newGlobalConfig.roll,
        time: newGlobalConfig.time,
      });
    } else if (onLocalVmConfigChange) {
      // Update local configuration for specific trace
      const newLocalConfig = {
        ...effectiveVmConfig,
        [property]: !effectiveVmConfig[property],
      };
      onLocalVmConfigChange(index, newLocalConfig);
    }
  };

  const enabledButtons = [];
  const configToDisplay =
    mode === "global" ? globalVmConfig : effectiveVmConfig;

  // In global mode, show all buttons. In local mode, only show buttons that are enabled in global config
  const shouldShowPrank = mode === "global" || globalVmConfig.prank;
  const shouldShowRoll = mode === "global" || globalVmConfig.roll;
  const shouldShowTime = mode === "global" || globalVmConfig.time;

  if (shouldShowPrank) {
    enabledButtons.push(
      <AppButton
        key="prank"
        variant={configToDisplay.prank ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("prank")}
      >
        <span>Use vm.prank</span>
      </AppButton>
    );
  }

  if (shouldShowRoll) {
    enabledButtons.push(
      <AppButton
        key="roll"
        variant={configToDisplay.roll ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("roll")}
      >
        <span>Use vm.roll</span>
      </AppButton>
    );
  }

  if (shouldShowTime) {
    enabledButtons.push(
      <AppButton
        key="time"
        variant={configToDisplay.time ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("time")}
      >
        <span>Use vm.warp</span>
      </AppButton>
    );
  }

  if (!isInitialized) {
    return (
      <div className={`flex gap-2 max-[900px]:hidden ${className}`}>
        <span className="text-sm text-fore-neutral-secondary">
          Loading VM options...
        </span>
      </div>
    );
  }

  if (enabledButtons.length === 0) {
    return (
      <div className={`flex gap-2 max-[900px]:hidden ${className}`}>
        <span className="text-sm text-fore-neutral-secondary">
          No VM options enabled
        </span>
      </div>
    );
  }

  return (
    <div className={`flex gap-2 max-[900px]:hidden ${className}`}>
      {enabledButtons}
    </div>
  );
}
