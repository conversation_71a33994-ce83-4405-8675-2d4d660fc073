import { useState, useEffect } from "react";
import { useFormContext, useFieldArray } from "react-hook-form";

import { AppButton } from "../app-button";
import { AppInput } from "../app-input";
import { AppSwitch } from "../app-switch";
import { Body3, H2 } from "../app-typography";
import { FORM_STYLES } from "./constants";
import type { CreateJobFormValues } from "./types";

interface DynamicReplacementSectionProps {
  className?: string;
}

export const DynamicReplacementSection = ({
  className = "",
}: DynamicReplacementSectionProps) => {
  const [showDynamicReplacement, setShowDynamicReplacement] = useState(false);

  // Always call hooks at the top level
  const { register, control, watch, setValue } =
    useFormContext<CreateJobFormValues>();

  const { fields, append, remove } = useFieldArray({
    control,
    name: "fields",
  });

  const watchedFields = watch("fields");

  useEffect(() => {
    // Filter out incomplete field groups
    if (!watchedFields) return;

    const validFields = watchedFields.filter(
      (field) =>
        field.variableName.trim() &&
        field.interface.trim() &&
        field.value.trim()
    );

    const prepContract = validFields.map((field) => ({
      target: `${field.variableName} = ${field.interface}`,
      replacement: `${field.variableName} = ${field.interface}(${field.value});`,
      endOfTargetMarker: "[^;]*",
      targetContract: "Setup.sol",
    }));
    setValue("prepareContracts", prepContract);

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [watchedFields, setValue]);

  return (
    <div className={className}>
      <div className={FORM_STYLES.divider} />

      <div className="mb-6 flex items-center gap-4">
        <Body3 color="secondary" className="font-semibold">
          Enable Dynamic Replacement:
        </Body3>
        <AppSwitch
          enabled={showDynamicReplacement}
          onChange={(e) => setShowDynamicReplacement(e.target.checked)}
        />
      </div>

      {showDynamicReplacement && (
        <>
          <div className="border-status-warning bg-status-warning/10 mb-6 rounded-lg border p-4">
            <H2 className="mb-2" color="primary">
              Dynamic Replacement Mode
            </H2>
            <div className="space-y-2">
              <Body3 color="secondary">
                • Dynamic Replacement is in EXPERIMENTAL mode
              </Body3>
              <Body3 color="secondary">
                • All variables Dynamically Replaced MUST be in the `Setup.sol`
                file
              </Body3>
              <Body3 color="secondary">
                • Make sure you have no clashing file!
              </Body3>
            </div>
          </div>
          <div className="space-y-4">
            <div className="space-y-4">
              {fields.map((field, index) => (
                <div key={field.id} className={FORM_STYLES.inputGroupTriple}>
                  <div className={FORM_STYLES.fieldContainer}>
                    <AppInput
                      className={FORM_STYLES.input}
                      label="Variable Name"
                      {...register(`fields.${index}.variableName` as const)}
                      type="text"
                      defaultValue={field.variableName}
                    />
                  </div>
                  <div className={FORM_STYLES.fieldContainer}>
                    <AppInput
                      className={FORM_STYLES.input}
                      label="Interface"
                      {...register(`fields.${index}.interface` as const)}
                      type="text"
                      defaultValue={field.interface}
                    />
                  </div>
                  <div className={FORM_STYLES.fieldContainer}>
                    <AppInput
                      className={FORM_STYLES.input}
                      label="Value"
                      {...register(`fields.${index}.value` as const)}
                      type="text"
                      defaultValue={field.value}
                    />
                    <button
                      type="button"
                      onClick={() => remove(index)}
                      className="mt-2 text-sm text-accent-primary underline hover:text-accent-secondary"
                      title="Delete Field Group"
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
            </div>

            <AppButton
              type="button"
              variant="secondary"
              onClick={() =>
                append({ variableName: "", interface: "", value: "" })
              }
              className="mt-4"
            >
              Add More Fields
            </AppButton>
          </div>
        </>
      )}
    </div>
  );
};
