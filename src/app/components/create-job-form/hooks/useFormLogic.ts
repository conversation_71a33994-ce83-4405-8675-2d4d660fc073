import { useForm } from "react-hook-form";
import type { CreateJobFormValues, FormMode } from "../types";
import { DEFAULT_FORM_VALUES } from "../constants";

export const useFormLogic = (mode: FormMode = "job") => {
  const defaultValues = {
    ...DEFAULT_FORM_VALUES,
    ...(mode === "job" ? { label: "" } : { displayName: "" }),
  };

  const form = useForm<CreateJobFormValues>({
    defaultValues,
  });

  const {
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    formState: { errors, isSubmitting },
  } = form;

  return {
    form,
    register,
    handleSubmit,
    setValue,
    setError,
    watch,
    errors,
    isSubmitting,
  };
};
